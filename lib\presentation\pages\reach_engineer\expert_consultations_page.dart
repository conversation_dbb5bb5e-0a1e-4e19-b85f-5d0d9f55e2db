import '../../../imports.dart';

/// صفحة إدارة الاستشارات للخبير
///
/// تتيح للخبير عرض وإدارة الاستشارات الواردة إليه
/// مع إمكانية القبول والرفض والرد على الاستشارات
class ExpertConsultationsPage extends StatefulWidget {
  /// معرف الخبير
  final String expertId;

  /// اسم الخبير
  final String expertName;

  /// إنشاء صفحة إدارة الاستشارات للخبير
  const ExpertConsultationsPage({
    super.key,
    required this.expertId,
    required this.expertName,
  });

  @override
  State<ExpertConsultationsPage> createState() =>
      _ExpertConsultationsPageState();
}

class _ExpertConsultationsPageState extends State<ExpertConsultationsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _searchController = TextEditingController();

  List<ConsultationModel> _allConsultations = [];
  List<ConsultationModel> _filteredConsultations = [];
  Map<String, int>? _statistics;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadConsultations();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: defaultAppBar(
        context: context,
        titel: 'إدارة الاستشارات',
        color: AppColors.primary,
      ),
      body: BlocListener<ConsultationsCubit, ConsultationsState>(
        listener: _handleConsultationState,
        child: Column(
          children: [
            // شريط البحث
            _buildSearchBar(),

            // التبويبات
            _buildTabBar(),

            // الإحصائيات
            if (_statistics != null) _buildStatistics(),

            // محتوى الاستشارات
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildConsultationsList(), // جميع الاستشارات
                  _buildConsultationsList(
                    status: ConsultationStatus.pending,
                  ), // جديدة
                  _buildConsultationsList(
                    status: ConsultationStatus.pending,
                  ), // قيد المعالجة
                  _buildConsultationsList(
                    status: ConsultationStatus.responded,
                  ), // تم الرد
                  _buildConsultationsList(
                    status: ConsultationStatus.completed,
                  ), // مكتملة
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريط البحث
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.surface,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في الاستشارات...',
          prefixIcon: const Icon(Icons.search, color: AppColors.textSecondary),
          suffixIcon:
              _searchController.text.isNotEmpty
                  ? IconButton(
                    icon: const Icon(
                      Icons.clear,
                      color: AppColors.textSecondary,
                    ),
                    onPressed: () {
                      _searchController.clear();
                      _filterConsultations();
                    },
                  )
                  : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: AppColors.border),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: AppColors.border),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: AppColors.primary, width: 2),
          ),
          filled: true,
          fillColor: AppColors.background,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        onChanged: (value) {
          setState(() {});
          _filterConsultations();
        },
      ),
    );
  }

  /// بناء شريط التبويبات
  Widget _buildTabBar() {
    return Container(
      color: AppColors.surface,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        isScrollable: true,
        tabs: [
          Tab(text: 'الكل', icon: Icon(Icons.list_alt, size: 20)),
          Tab(text: 'جديدة', icon: Icon(Icons.new_releases, size: 20)),
          Tab(
            text: 'قيد المعالجة',
            icon: Icon(Icons.hourglass_empty, size: 20),
          ),
          Tab(text: 'تم الرد', icon: Icon(Icons.reply, size: 20)),
          Tab(text: 'مكتملة', icon: Icon(Icons.check_circle, size: 20)),
        ],
      ),
    );
  }

  /// بناء الإحصائيات
  Widget _buildStatistics() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: AppColors.surface,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            'المجموع',
            _statistics!['total']?.toString() ?? '0',
            Icons.list_alt,
            AppColors.primary,
          ),
          _buildStatItem(
            'جديدة',
            _statistics!['pending']?.toString() ?? '0',
            Icons.new_releases,
            AppColors.warning,
          ),
          _buildStatItem(
            'تم الرد',
            _statistics!['responded']?.toString() ?? '0',
            Icons.reply,
            AppColors.info,
          ),
          _buildStatItem(
            'مكتملة',
            _statistics!['completed']?.toString() ?? '0',
            Icons.check_circle,
            AppColors.success,
          ),
        ],
      ),
    );
  }

  /// بناء عنصر إحصائية
  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyles.of(
            context,
          ).headlineSmall(fontWeight: FontWeight.bold, color: color),
        ),
        Text(
          label,
          style: TextStyles.of(
            context,
          ).bodySmall(color: AppColors.textSecondary),
        ),
      ],
    );
  }

  /// بناء قائمة الاستشارات
  Widget _buildConsultationsList({ConsultationStatus? status}) {
    return BlocBuilder<ConsultationsCubit, ConsultationsState>(
      builder: (context, state) {
        if (state is ConsultationsLoading) {
          return  Center(child: SkeletonLoading(height: 150,width: 150,));
        }

        if (state is ConsultationsError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 64, color: AppColors.error),
                const SizedBox(height: 16),
                Text(
                  'حدث خطأ في تحميل الاستشارات',
                  style: TextStyles.of(
                    context,
                  ).bodyLarge(color: AppColors.error),
                ),
                const SizedBox(height: 8),
                Text(
                  state.message,
                  style: TextStyles.of(
                    context,
                  ).bodyMedium(color: AppColors.textSecondary),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadConsultations,
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          );
        }

        final consultations = _getFilteredConsultations(status);

        if (consultations.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.assignment_outlined,
                  size: 64,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(height: 16),
                Text(
                  status == null
                      ? 'لا توجد استشارات'
                      : 'لا توجد استشارات ${_getStatusText(status)}',
                  style: TextStyles.of(
                    context,
                  ).bodyLarge(color: AppColors.textSecondary),
                ),
                const SizedBox(height: 8),
                Text(
                  'ستظهر الاستشارات الجديدة هنا',
                  style: TextStyles.of(
                    context,
                  ).bodyMedium(color: AppColors.textSecondary),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async => _loadConsultations(),
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: consultations.length,
            itemBuilder: (context, index) {
              final consultation = consultations[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Card(
                  elevation: 2,
                  child: ListTile(
                    title: Text(consultation.title),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(consultation.description),
                        const SizedBox(height: 4),
                        Text(
                          'الحالة: ${_getStatusText(consultation.status)}',
                          style: TextStyle(
                            color: _getStatusColor(consultation.status),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    trailing:
                        consultation.status == ConsultationStatus.pending
                            ? PopupMenuButton<String>(
                              onSelected: (value) {
                                switch (value) {
                                  case 'accept':
                                    _acceptConsultation(consultation);
                                    break;
                                  case 'reject':
                                    _rejectConsultation(consultation);
                                    break;
                                  case 'respond':
                                    _respondToConsultation(consultation);
                                    break;
                                }
                              },
                              itemBuilder:
                                  (context) => [
                                    const PopupMenuItem(
                                      value: 'accept',
                                      child: Text('قبول'),
                                    ),
                                    const PopupMenuItem(
                                      value: 'reject',
                                      child: Text('رفض'),
                                    ),
                                    const PopupMenuItem(
                                      value: 'respond',
                                      child: Text('رد'),
                                    ),
                                  ],
                            )
                            : null,
                    onTap: () => _viewConsultationDetails(consultation),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// الحصول على الاستشارات المفلترة
  List<ConsultationModel> _getFilteredConsultations(
    ConsultationStatus? status,
  ) {
    var consultations = _filteredConsultations;

    if (status != null) {
      consultations = consultations.where((c) => c.status == status).toList();
    }

    return consultations;
  }

  /// الحصول على نص الحالة
  String _getStatusText(ConsultationStatus status) {
    switch (status) {
      case ConsultationStatus.pending:
        return 'جديدة';
      case ConsultationStatus.responded:
        return 'تم الرد عليها';
      case ConsultationStatus.completed:
        return 'مكتملة';
      case ConsultationStatus.cancelled:
        return 'ملغية';
    }
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(ConsultationStatus status) {
    switch (status) {
      case ConsultationStatus.pending:
        return Colors.orange;
      case ConsultationStatus.responded:
        return Colors.blue;
      case ConsultationStatus.completed:
        return Colors.green;
      case ConsultationStatus.cancelled:
        return Colors.red;
    }
  }

  /// تحميل الاستشارات
  void _loadConsultations() {
    context.read<ConsultationsCubit>().loadExpertConsultations(widget.expertId);
  }

  /// فلترة الاستشارات
  void _filterConsultations() {
    final query = _searchController.text.toLowerCase();

    if (query.isEmpty) {
      _filteredConsultations = List.from(_allConsultations);
    } else {
      _filteredConsultations =
          _allConsultations.where((consultation) {
            return consultation.title.toLowerCase().contains(query) ||
                consultation.description.toLowerCase().contains(query) ||
                consultation.farmerName.toLowerCase().contains(query) ||
                consultation.category.toLowerCase().contains(query);
          }).toList();
    }

    setState(() {});
  }

  /// عرض تفاصيل الاستشارة
  void _viewConsultationDetails(ConsultationModel consultation) {
    // البحث عن بيانات الخبير
    final expert = AgriculturalExpertModel.empty().copyWith(
      id: consultation.expertId,
      name: consultation.expertName,
    );

    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => ConsultationDetailsPage(
              consultation: consultation,
              expert: expert,
              currentFarmerId: consultation.farmerId,
              currentFarmerName: consultation.farmerName,
            ),
      ),
    );
  }

  /// قبول الاستشارة
  void _acceptConsultation(ConsultationModel consultation) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('قبول الاستشارة'),
            content: Text('هل تريد قبول استشارة "${consultation.title}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // تطبيق قبول الاستشارة
                  _acceptConsultation(consultation);
                },
                child: const Text('قبول'),
              ),
            ],
          ),
    );
  }

  /// رفض الاستشارة
  void _rejectConsultation(ConsultationModel consultation) {
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('رفض الاستشارة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('هل تريد رفض استشارة "${consultation.title}"؟'),
                const SizedBox(height: 16),
                TextField(
                  controller: reasonController,
                  decoration: const InputDecoration(
                    labelText: 'سبب الرفض (اختياري)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // تطبيق رفض الاستشارة
                  _rejectConsultation(consultation);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                ),
                child: const Text('رفض'),
              ),
            ],
          ),
    );
  }

  /// الرد على الاستشارة
  void _respondToConsultation(ConsultationModel consultation) {
    final responseController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('الرد على الاستشارة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('الرد على استشارة "${consultation.title}"'),
                const SizedBox(height: 16),
                TextField(
                  controller: responseController,
                  decoration: const InputDecoration(
                    labelText: 'نص الرد',
                    border: OutlineInputBorder(),
                    hintText: 'اكتب ردك على الاستشارة...',
                  ),
                  maxLines: 5,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  if (responseController.text.trim().isNotEmpty) {
                    Navigator.of(context).pop();
                    // تطبيق الرد على الاستشارة
                    _respondToConsultation(consultation, responseController.text.trim());
                  }
                },
                child: const Text('إرسال الرد'),
              ),
            ],
          ),
    );
  }

  /// معالجة حالة الاستشارة
  void _handleConsultationState(
    BuildContext context,
    ConsultationsState state,
  ) {
    if (state is ConsultationsLoaded) {
      _allConsultations = state.consultations;
      // _statistics = state.statistics; // مؤقتاً معطل
      _filterConsultations();
    } else if (state is ConsultationUpdated) {
      MessageHelper.showSuccess(context, 'تم تحديث الاستشارة');
      _loadConsultations(); // إعادة تحميل البيانات
    } else if (state is ConsultationsError) {
      MessageHelper.showError(context, state.message);
    }
  }

  /// قبول الاستشارة
  void _acceptConsultation(ConsultationModel consultation) {
    try {
      // تطبيق منطق قبول الاستشارة - تحديث الحالة إلى "تم الرد"
      context.read<ConsultationsCubit>().updateConsultationStatus(
        consultation.id,
        ConsultationStatus.responded,
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم قبول الاستشارة بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في قبول الاستشارة: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// رفض الاستشارة
  void _rejectConsultation(ConsultationModel consultation) {
    try {
      // تطبيق منطق رفض الاستشارة - تحديث الحالة إلى "ملغاة"
      context.read<ConsultationsCubit>().updateConsultationStatus(
        consultation.id,
        ConsultationStatus.cancelled,
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم رفض الاستشارة'),
          backgroundColor: Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في رفض الاستشارة: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// الرد على الاستشارة
  void _respondToConsultation(ConsultationModel consultation, String response) {
    try {
      // تطبيق منطق الرد على الاستشارة - تحديث الحالة إلى "تم الرد"
      context.read<ConsultationsCubit>().updateConsultationStatus(
        consultation.id,
        ConsultationStatus.responded,
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إرسال الرد بنجاح'),
          backgroundColor: Colors.blue,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إرسال الرد: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
